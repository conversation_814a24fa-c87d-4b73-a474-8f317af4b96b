package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskProcessor defines the interface for processing different types of tasks
type TaskProcessor interface {
	ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error
	CanProcess(task *model.ActivityTask) bool
	GetTaskType() string
}

// DailyTaskProcessor handles daily tasks
type DailyTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewDailyTaskProcessor creates a new DailyTaskProcessor
func NewDailyTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &DailyTaskProcessor{
		service: service,
	}
}

// ProcessTask processes daily tasks
func (p *DailyTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Use TaskIdentifier for better maintainability
	if task.TaskIdentifier == nil {
		return fmt.Errorf("daily task must have a task identifier set: %s", task.Name)
	}

	switch *task.TaskIdentifier {
	case model.TaskIDDailyCheckin:
		return p.processDailyCheckIn(ctx, userID, task)
	case model.TaskIDMemeTradeDaily:
		return p.processMemeTrade(ctx, userID, task, data)
	case model.TaskIDPerpetualTradeDaily:
		return p.processPerpetualTrade(ctx, userID, task, data)
	case model.TaskIDMarketPageView:
		return p.processMarketCheck(ctx, userID, task)
	case model.TaskIDConsecutiveCheckin:
		return p.processConsecutiveCheckIn(ctx, userID, task, data)
	case model.TaskIDConsecutiveCheckin3:
		return p.processConsecutiveCheckIn(ctx, userID, task, data)
	case model.TaskIDConsecutiveCheckin7:
		return p.processConsecutiveCheckIn(ctx, userID, task, data)
	case model.TaskIDConsecutiveCheckin30:
		return p.processConsecutiveCheckIn(ctx, userID, task, data)
	case model.TaskIDConsecutiveTradingDays:
		return p.processConsecutiveTradingDays(ctx, userID, task, data)
	default:
		return fmt.Errorf("unsupported daily task identifier: %s", *task.TaskIdentifier)
	}
}

// CanProcess checks if this processor can handle the task
func (p *DailyTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.Category.Name == model.CategoryDaily
}

// GetTaskType returns the task type this processor handles
func (p *DailyTaskProcessor) GetTaskType() string {
	return string(model.CategoryDaily)
}

// processDailyCheckIn handles daily check-in task
func (p *DailyTaskProcessor) processDailyCheckIn(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) error {
	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete daily check-in: %w", err)
	}

	global.GVA_LOG.Info("Daily check-in completed", zap.String("user_id", userID.String()))
	return nil
}

// processMemeTrade handles MEME trade completion
func (p *DailyTaskProcessor) processMemeTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Auto-initialize task progress if user doesn't have it
			progress, err = p.service.InitializeTaskProgress(ctx, userID, task.ID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("MEME trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data - now we verify based on actual transaction records
	// This will be called when affiliate_transactions table has new MEME trade records
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid MEME trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "MEME" {
		return fmt.Errorf("invalid trade type for MEME task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"processor":  "DailyTaskProcessor",
	}
	if err := p.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete MEME trade task with points: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processPerpetualTrade handles perpetual trade completion
func (p *DailyTaskProcessor) processPerpetualTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("Perpetual trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data - now we verify based on actual transaction records
	// This will be called when hyper_liquid_transactions table has new filled records
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid perpetual trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "PERPETUAL" {
		return fmt.Errorf("invalid trade type for perpetual task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"processor":  "DailyTaskProcessor",
	}
	if err := p.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete perpetual trade task with points: %w", err)
	}

	global.GVA_LOG.Info("Perpetual trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processMarketCheck handles market condition check
func (p *DailyTaskProcessor) processMarketCheck(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) error {
	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete market check task: %w", err)
	}

	global.GVA_LOG.Info("Market check task completed", zap.String("user_id", userID.String()))
	return nil
}

// processConsecutiveCheckIn handles consecutive check-in tasks
func (p *DailyTaskProcessor) processConsecutiveCheckIn(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user checked in today
	today := time.Now().Truncate(24 * time.Hour)
	lastCheckIn := time.Time{}
	if progress.LastCompletedAt != nil {
		lastCheckIn = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If last check-in was yesterday, increment streak
	yesterday := today.Add(-24 * time.Hour)
	if lastCheckIn.Equal(yesterday) {
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastCheckIn.Equal(today) {
		// Reset streak if gap > 1 day
		if err := p.service.ResetStreak(ctx, userID, task.ID); err != nil {
			return fmt.Errorf("failed to reset streak: %w", err)
		}
		// Start new streak
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	}

	// Update progress
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment progress: %w", err)
	}

	// Check if milestone reached (3, 7, or 30 days)
	updatedProgress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	milestones := []int{3, 7, 30}
	for _, milestone := range milestones {
		if updatedProgress.StreakCount == milestone {
			// Award bonus points for milestone
			bonusPoints := map[int]int{3: 50, 7: 200, 30: 1000}
			if err := p.service.AddPoints(ctx, userID, bonusPoints[milestone], fmt.Sprintf("consecutive_checkin_%d", milestone)); err != nil {
				global.GVA_LOG.Error("Failed to award milestone bonus", zap.Error(err))
			}
			global.GVA_LOG.Info("Consecutive check-in milestone reached",
				zap.String("user_id", userID.String()),
				zap.Int("milestone", milestone),
				zap.Int("bonus_points", bonusPoints[milestone]))
			break
		}
	}

	return nil
}

// processConsecutiveTradingDays handles consecutive trading days tasks
func (p *DailyTaskProcessor) processConsecutiveTradingDays(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user traded today
	today := time.Now().Truncate(24 * time.Hour)
	lastTradingDay := time.Time{}
	if progress.LastCompletedAt != nil {
		lastTradingDay = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If last trading day was yesterday, increment streak
	yesterday := today.Add(-24 * time.Hour)
	if lastTradingDay.Equal(yesterday) {
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastTradingDay.Equal(today) {
		// Reset streak if gap > 1 day
		if err := p.service.ResetStreak(ctx, userID, task.ID); err != nil {
			return fmt.Errorf("failed to reset streak: %w", err)
		}
		// Start new streak
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	}

	// Update progress
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment progress: %w", err)
	}

	// Check if milestone reached (3, 7, 15, or 30 days)
	updatedProgress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	milestones := []int{3, 7, 15, 30}
	for _, milestone := range milestones {
		if updatedProgress.StreakCount == milestone {
			// Award bonus points for milestone
			bonusPoints := map[int]int{3: 50, 7: 200, 15: 1000, 30: 2000}
			if err := p.service.AddPoints(ctx, userID, bonusPoints[milestone], fmt.Sprintf("consecutive_trading_%d", milestone)); err != nil {
				global.GVA_LOG.Error("Failed to award milestone bonus", zap.Error(err))
			}
			global.GVA_LOG.Info("Consecutive trading milestone reached",
				zap.String("user_id", userID.String()),
				zap.Int("milestone", milestone),
				zap.Int("bonus_points", bonusPoints[milestone]))
			break
		}
	}

	return nil
}

// CommunityTaskProcessor handles community tasks
type CommunityTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewCommunityTaskProcessor creates a new CommunityTaskProcessor
func NewCommunityTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &CommunityTaskProcessor{
		service: service,
	}
}

// ProcessTask processes community tasks
func (p *CommunityTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Use enum-based task identification for better maintainability
	if task.TaskIdentifier != nil {
		switch *task.TaskIdentifier {
		case model.TaskIDTwitterFollow:
			return p.processTwitterFollow(ctx, userID, task, data)
		case model.TaskIDTwitterRetweet:
			return p.processTwitterRetweet(ctx, userID, task, data)
		case model.TaskIDTwitterLike:
			return p.processTwitterLike(ctx, userID, task, data)
		case model.TaskIDTelegramJoin:
			return p.processTelegramJoin(ctx, userID, task, data)
		case model.TaskIDInviteFriends:
			return p.processInviteFriends(ctx, userID, task, data)
		case model.TaskIDShareReferral:
			return p.processShareReferralLink(ctx, userID, task, data)
		default:
			return fmt.Errorf("unsupported community task identifier: %s", *task.TaskIdentifier)
		}
	}

	// If no task identifier is set, return error
	return fmt.Errorf("community task must have a task identifier set: %s", task.Name)
}

// CanProcess checks if this processor can handle the task
func (p *CommunityTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.Category.Name == model.CategoryCommunity
}

// GetTaskType returns the task type this processor handles
func (p *CommunityTaskProcessor) GetTaskType() string {
	return "COMMUNITY"
}

// processTwitterFollow handles Twitter follow task
func (p *CommunityTaskProcessor) processTwitterFollow(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Create pending community task instead of completing immediately
	// This task will be completed after 2 minutes by background job

	_, err := p.service.CreatePendingCommunityTask(ctx, userID, task.ID, data)
	if err != nil {
		return fmt.Errorf("failed to create pending Twitter follow task: %w", err)
	}

	global.GVA_LOG.Info("Twitter follow task set to pending (2 minutes wait)",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()))
	return nil
}

// processTwitterRetweet handles Twitter retweet task
func (p *CommunityTaskProcessor) processTwitterRetweet(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Create pending community task instead of completing immediately
	// This task will be completed after 2 minutes by background job

	_, err := p.service.CreatePendingCommunityTask(ctx, userID, task.ID, data)
	if err != nil {
		return fmt.Errorf("failed to create pending Twitter retweet task: %w", err)
	}

	global.GVA_LOG.Info("Twitter retweet task set to pending (2 minutes wait)",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()))
	return nil
}

// processTwitterLike handles Twitter like task
func (p *CommunityTaskProcessor) processTwitterLike(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Create pending community task instead of completing immediately
	// This task will be completed after 2 minutes by background job

	_, err := p.service.CreatePendingCommunityTask(ctx, userID, task.ID, data)
	if err != nil {
		return fmt.Errorf("failed to create pending Twitter like task: %w", err)
	}

	global.GVA_LOG.Info("Twitter like task set to pending (2 minutes wait)",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()))
	return nil
}

// processTelegramJoin handles Telegram join task
func (p *CommunityTaskProcessor) processTelegramJoin(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Create pending community task instead of completing immediately
	// This task will be completed after 2 minutes by background job

	_, err := p.service.CreatePendingCommunityTask(ctx, userID, task.ID, data)
	if err != nil {
		return fmt.Errorf("failed to create pending Telegram join task: %w", err)
	}

	global.GVA_LOG.Info("Telegram join task set to pending (2 minutes wait)",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()))
	return nil
}

// processInviteFriends handles invite friends task
func (p *CommunityTaskProcessor) processInviteFriends(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Verify friend registration
	friendID, ok := data["friend_id"].(string)
	if !ok {
		return fmt.Errorf("friend ID not provided")
	}

	friendUUID, err := uuid.Parse(friendID)
	if err != nil {
		return fmt.Errorf("invalid friend ID: %w", err)
	}

	// Verify that the friend was actually referred by this user
	// This would typically check the referral system

	// Award points for successful referral
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment invite progress: %w", err)
	}

	global.GVA_LOG.Info("Friend invite task completed",
		zap.String("user_id", userID.String()),
		zap.String("friend_id", friendUUID.String()))
	return nil
}

// processShareReferralLink handles share referral link task
func (p *CommunityTaskProcessor) processShareReferralLink(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// For daily sharing task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete share referral link task: %w", err)
	}

	global.GVA_LOG.Info("Share referral link task completed", zap.String("user_id", userID.String()))
	return nil
}

// TradingTaskProcessor handles trading tasks
type TradingTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewTradingTaskProcessor creates a new TradingTaskProcessor
func NewTradingTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &TradingTaskProcessor{
		service: service,
	}
}

// ProcessTask processes trading tasks
func (p *TradingTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Use TaskIdentifier for better maintainability
	if task.TaskIdentifier == nil {
		return fmt.Errorf("trading task must have a task identifier set: %s", task.Name)
	}

	switch *task.TaskIdentifier {
	case model.TaskIDMemeTradeDaily:
		return p.processMemeTrade(ctx, userID, task, data)
	case model.TaskIDPerpetualTradeDaily:
		return p.processPerpetualTrade(ctx, userID, task, data)
	case model.TaskIDTradingPoints:
		return p.processTradingPoints(ctx, userID, task, data)
	case model.TaskIDAccumulatedTrading10K:
		return p.processAccumulatedTrading(ctx, userID, task, data, 10000)
	case model.TaskIDAccumulatedTrading50K:
		return p.processAccumulatedTrading(ctx, userID, task, data, 50000)
	case model.TaskIDAccumulatedTrading100K:
		return p.processAccumulatedTrading(ctx, userID, task, data, 100000)
	case model.TaskIDAccumulatedTrading500K:
		return p.processAccumulatedTrading(ctx, userID, task, data, 500000)
	default:
		return fmt.Errorf("unsupported trading task identifier: %s", *task.TaskIdentifier)
	}
}

// CanProcess checks if this processor can handle the task
func (p *TradingTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.Category.Name == model.CategoryTrading
}

// GetTaskType returns the task type this processor handles
func (p *TradingTaskProcessor) GetTaskType() string {
	return "TRADING"
}

// processMemeTrade handles MEME trade completion for trading tasks
func (p *TradingTaskProcessor) processMemeTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Auto-initialize task progress if user doesn't have it
			progress, err = p.service.InitializeTaskProgress(ctx, userID, task.ID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("MEME trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid MEME trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "MEME" {
		return fmt.Errorf("invalid trade type for MEME task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"processor":  "TradingTaskProcessor",
	}
	if err := p.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete MEME trade task with points: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processPerpetualTrade handles perpetual trade completion for trading tasks
func (p *TradingTaskProcessor) processPerpetualTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("Perpetual trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid perpetual trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "PERPETUAL" {
		return fmt.Errorf("invalid trade type for perpetual task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"processor":  "TradingTaskProcessor",
	}
	if err := p.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete perpetual trade task with points: %w", err)
	}

	global.GVA_LOG.Info("Perpetual trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processTradingPoints handles trading points based on volume
func (p *TradingTaskProcessor) processTradingPoints(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trading volume")
	}

	// Get trade type to apply different weights
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		tradeType = "MEME" // Default to MEME if not specified
	}

	// Only process MEME trading volume for Activity Cashback
	// Derivatives trading is excluded from volume calculations
	if tradeType != "MEME" {
		global.GVA_LOG.Info("Skipping non-MEME trade for Activity Cashback",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume))
		return nil
	}

	// No weight adjustment needed since we only process MEME trades
	weightedVolume := volume

	// Calculate points based on weighted volume tiers
	var points int
	switch {
	case weightedVolume >= 10000:
		points = 40
	case weightedVolume >= 3000:
		points = 25
	case weightedVolume >= 500:
		points = 12
	case weightedVolume >= 100:
		points = 5
	case weightedVolume >= 1:
		points = 1
	default:
		points = 0
	}

	if points > 0 {
		// Award points directly (not through task completion)
		if err := p.service.AddPoints(ctx, userID, points, fmt.Sprintf("trading_volume_%.2f", volume)); err != nil {
			return fmt.Errorf("failed to award trading points: %w", err)
		}

		// Update progress
		if err := p.service.IncrementProgress(ctx, userID, task.ID, points); err != nil {
			return fmt.Errorf("failed to update trading points progress: %w", err)
		}

		global.GVA_LOG.Info("Trading points awarded",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("original_volume", volume),
			zap.Float64("weighted_volume", weightedVolume),
			zap.Int("points", points))
	}

	return nil
}

// processAccumulatedTrading handles accumulated trading milestones
func (p *TradingTaskProcessor) processAccumulatedTrading(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}, milestone float64) error {
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trading volume")
	}

	// Get trade type to apply different weights
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		tradeType = "MEME" // Default to MEME if not specified
	}

	// Only process MEME trading volume for Activity Cashback
	// Derivatives trading is excluded from volume calculations
	if tradeType != "MEME" {
		global.GVA_LOG.Info("Skipping non-MEME trade for accumulated volume task",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume))
		return nil
	}

	// No weight adjustment needed since we only process MEME trades
	weightedVolume := volume

	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if already completed
	if progress.IsCompleted() {
		return nil // Already completed
	}

	// Update accumulated volume using weighted volume
	newTotal := float64(progress.ProgressValue) + weightedVolume
	if err := p.service.SetProgress(ctx, userID, task.ID, int(newTotal)); err != nil {
		return fmt.Errorf("failed to update accumulated trading progress: %w", err)
	}

	// Check if milestone reached
	if newTotal >= milestone {
		verificationData := map[string]interface{}{
			"milestone":             milestone,
			"total_weighted_volume": newTotal,
			"weighted_volume_added": weightedVolume,
			"trade_type":            tradeType,
			"method":                "automated_nats_event",
			"processor":             "TradingTaskProcessor",
		}
		if err := p.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
			return fmt.Errorf("failed to complete accumulated trading task with points: %w", err)
		}

		global.GVA_LOG.Info("Accumulated trading milestone reached",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("milestone", milestone),
			zap.Float64("weighted_volume_added", weightedVolume),
			zap.Float64("total_weighted_volume", newTotal),
			zap.Int("points", task.Points))
	}

	return nil
}

// TaskProcessorManager manages all task processors
type TaskProcessorManager struct {
	processors []TaskProcessor
	service    ActivityCashbackServiceInterface
	registry   *TaskRegistry // Add task registry
}

// NewTaskProcessorManager creates a new TaskProcessorManager
func NewTaskProcessorManager(service ActivityCashbackServiceInterface) *TaskProcessorManager {
	registry := NewTaskRegistry(service)
	return &TaskProcessorManager{
		processors: []TaskProcessor{
			NewDailyTaskProcessor(service),
			NewCommunityTaskProcessor(service),
			NewTradingTaskProcessor(service),
		},
		service:  service,
		registry: registry,
	}
}

// ProcessTask processes a task using the new registry system
func (m *TaskProcessorManager) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Use only the new registry-based approach
	return m.registry.ProcessTask(ctx, userID, task, data)
}

// ProcessTaskByIdentifier processes a task by its identifier
func (m *TaskProcessorManager) ProcessTaskByIdentifier(ctx context.Context, userID uuid.UUID, identifier model.TaskIdentifier, categoryName model.TaskCategoryName, data map[string]interface{}) error {
	return m.registry.ProcessTaskByIdentifier(ctx, userID, identifier, categoryName, data)
}

// ProcessTradingEvent processes trading events and updates relevant tasks
func (m *TaskProcessorManager) ProcessTradingEvent(ctx context.Context, userID uuid.UUID, tradeData map[string]interface{}) error {
	tradeType, ok := tradeData["trade_type"].(string)
	if !ok {
		return fmt.Errorf("trade type not specified")
	}

	volume, ok := tradeData["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trade volume")
	}

	global.GVA_LOG.Info("Processing trading event",
		zap.String("user_id", userID.String()),
		zap.String("trade_type", tradeType),
		zap.Float64("volume", volume),
		zap.Any("trade_data", tradeData))

	// Only process MEME trading volume for Activity Cashback
	// Derivatives trading is excluded from volume calculations
	if tradeType != "MEME" {
		global.GVA_LOG.Info("Skipping non-MEME trade for volume accumulation",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume))
		return nil
	}

	// Note: Trading volume accumulation is now handled by the Level Upgrade Task
	// which calculates SUM from daily_meme_volumes and updates user_tier_info.trading_volume_usd
	global.GVA_LOG.Info("Trading event processed for tasks",
		zap.String("user_id", userID.String()),
		zap.String("trade_type", tradeType),
		zap.Float64("volume", volume),
		zap.String("note", "Volume accumulation handled by Level Upgrade Task"))

	// Process daily trading tasks
	switch tradeType {
	case "MEME":
		// Try trading category first, then fallback to daily category for backward compatibility
		global.GVA_LOG.Info("Processing MEME trade task",
			zap.String("user_id", userID.String()),
			zap.String("identifier", string(model.TaskIDMemeTradeDaily)))

		if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, "trading", tradeData); err != nil {
			global.GVA_LOG.Info("Failed to process MEME trade task in trading category, trying daily category",
				zap.Error(err),
				zap.String("user_id", userID.String()))
			if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, "daily", tradeData); err != nil {
				global.GVA_LOG.Error("Failed to process MEME trade task in both categories",
					zap.Error(err),
					zap.String("user_id", userID.String()))
			} else {
				global.GVA_LOG.Info("Successfully processed MEME trade task in daily category",
					zap.String("user_id", userID.String()))
			}
		} else {
			global.GVA_LOG.Info("Successfully processed MEME trade task in trading category",
				zap.String("user_id", userID.String()))
		}
	case "PERPETUAL":
		// Try trading category first, then fallback to daily category for backward compatibility
		if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDPerpetualTradeDaily, "trading", tradeData); err != nil {
			global.GVA_LOG.Debug("Failed to process perpetual trade task in trading category, trying daily category", zap.Error(err))
			if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDPerpetualTradeDaily, "daily", tradeData); err != nil {
				global.GVA_LOG.Error("Failed to process perpetual trade task in both categories", zap.Error(err))
			}
		}
	}

	// Process trading points task
	if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDTradingPoints, "trading", tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process trading points task", zap.Error(err))
	}

	// Process accumulated trading tasks
	accumulatedTasks := []model.TaskIdentifier{
		model.TaskIDAccumulatedTrading10K,
		model.TaskIDAccumulatedTrading50K,
		model.TaskIDAccumulatedTrading100K,
		model.TaskIDAccumulatedTrading500K,
	}

	for _, taskIdentifier := range accumulatedTasks {
		if err := m.ProcessTaskByIdentifier(ctx, userID, taskIdentifier, "trading", tradeData); err != nil {
			global.GVA_LOG.Debug("Accumulated trading task not completed",
				zap.String("task", string(taskIdentifier)),
				zap.Error(err))
		}
	}

	// Process consecutive trading days task
	if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDConsecutiveTradingDays, "daily", tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process consecutive trading days task", zap.Error(err))
	}

	return nil
}
